import React, {use} from 'react';
import styles from '../styles/commonStyles';
import HeaderView from '../components/HeaderView/index';
import {RootStackParamList} from '../navigation/AppNavigator';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AssetImages} from '../utils/constants';
import {TopSafeAreaView} from '../components/CustomSafeAreaView/index';
import {useAppSelector} from '../redux/store/hooks';
import {FlatList} from 'react-native-gesture-handler';
import {VenueService} from '../services/api';

type Props = NativeStackScreenProps<RootStackParamList, 'Home'>;

const HomeScreen: React.FC<Props> = ({navigation}) => {
  const user = useAppSelector(state => state.user);
  console.log('🚀 ~ user:', user);

  const barlistWSCall = async () => {
    try {
      const response = VenueService.getBarList({
        latitude: '22.9857',
        longitude: '72.6432',
        search: '',
        showAll: 1,
        page: 1,
        serviceType: 'BOTH',
        distanceKm: 50,
      });
      console.log('response', response);
    } catch (error) {
      console.log('response', error);
    }
  };
  return (
    <>
      <TopSafeAreaView />
      <HeaderView />
    </>
  );
};

export default HomeScreen;
