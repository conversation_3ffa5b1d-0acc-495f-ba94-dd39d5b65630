import React, {useEffect, useState} from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {View, ActivityIndicator, Text} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import LoginScreen from '../screens/LoginScreen';
import HomeScreen from '../screens/HomeScreen';
import ForgotPasswordScreen from '../screens/ForgotPasswordScreen';
import OtpVerificationScreen from '../screens/OtpVerificationScreen';
import ResetPasswordScreen from '../screens/ResetPasswordScreen';
import {useAppSelector} from '../redux/store/hooks';
import {NetworkManager} from '../services/network';

export type RootStackParamList = {
  Login: undefined;
  Home: undefined;
  ForgotPassword: undefined;
  OtpVerification: {email: string};
  ResetPassword: {resetPasswordCode: number; id: string};
};

const Stack = createNativeStackNavigator<RootStackParamList>();

const AppNavigator = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const user = useAppSelector(state => state.user);

  // Check for stored access token on app startup
  useEffect(() => {
    checkAuthenticationStatus();
  }, []);

  const checkAuthenticationStatus = async () => {
    try {
      console.log('🔍 Checking authentication status...');

      // Method 1: Check Redux store first
      if (user.accessToken && user.accessToken.trim() !== '') {
        console.log('✅ Access token found in Redux store');
        setIsAuthenticated(true);
        setIsLoading(false);
        return;
      }

      // Method 2: Try to get access token from AsyncStorage
      try {
        const accessToken = await AsyncStorage.getItem('accessToken');

        if (accessToken && accessToken.trim() !== '') {
          console.log('✅ Access token found in AsyncStorage');

          // Set token in NetworkManager for API calls
          await NetworkManager.setAuthTokens({
            accessToken: accessToken,
            refreshToken: accessToken, // Using same token for refresh (adjust as needed)
          });

          setIsAuthenticated(true);
        } else {
          console.log('❌ No access token found in AsyncStorage');
          setIsAuthenticated(false);
        }
      } catch (storageError) {
        console.log(
          '⚠️ AsyncStorage not available, checking NetworkManager...',
        );

        // Method 3: Fallback to NetworkManager (memory-only)
        const networkToken = NetworkManager.getAccessToken();
        if (networkToken && networkToken.trim() !== '') {
          console.log('✅ Access token found in NetworkManager');
          setIsAuthenticated(true);
        } else {
          console.log('❌ No access token found anywhere');
          setIsAuthenticated(false);
        }
      }
    } catch (error) {
      console.error('❌ Error checking authentication:', error);
      setIsAuthenticated(false);
    } finally {
      setIsLoading(false);
    }
  };

  // Show loading screen while checking authentication
  if (isLoading) {
    return (
      <View
        style={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: '#fff',
        }}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={{marginTop: 16, fontSize: 16, color: '#666'}}>
          Loading...
        </Text>
      </View>
    );
  }

  // Determine initial route based on authentication status
  const initialRouteName = isAuthenticated ? 'Home' : 'Login';

  return (
    <Stack.Navigator
      initialRouteName={initialRouteName}
      screenOptions={{headerShown: false}}>
      <Stack.Screen name="Login" component={LoginScreen} />
      <Stack.Screen name="Home" component={HomeScreen} />
      <Stack.Screen name="ForgotPassword" component={ForgotPasswordScreen} />
      <Stack.Screen name="OtpVerification" component={OtpVerificationScreen} />
      <Stack.Screen name="ResetPassword" component={ResetPasswordScreen} />
    </Stack.Navigator>
  );
};
export default AppNavigator;
