/**
 * Authentication thunks for async operations
 */

import {createAsyncThunk} from '@reduxjs/toolkit';
import {AuthService} from '../../../services/api/APIServices';
import {LoginRequest, UserProfile} from '../../../types/User';
import {ApiResponse} from '../../../services/models/Response';

/**
 * Login thunk
 */
export const loginThunk = createAsyncThunk<
  UserProfile,
  LoginRequest,
  {
    rejectValue: string;
  }
>('auth/login', async (loginRequest: LoginRequest, {rejectWithValue}) => {
  try {
    const response: ApiResponse<UserProfile> = await AuthService.login(loginRequest);
    
    if (response.success && response.data) {
      return response.data;
    } else {
      return rejectWithValue(response.message || 'Login failed');
    }
  } catch (error: any) {
    // Handle different types of errors
    if (error.response?.data?.message) {
      return rejectWithValue(error.response.data.message);
    } else if (error.message) {
      return rejectWithValue(error.message);
    } else {
      return rejectWithValue('An unexpected error occurred during login');
    }
  }
});

/**
 * Logout thunk (for future use if logout API is needed)
 */
export const logoutThunk = createAsyncThunk<
  void,
  void,
  {
    rejectValue: string;
  }
>('auth/logout', async (_, {rejectWithValue}) => {
  try {
    // If you have a logout API endpoint, call it here
    // const response = await AuthService.logout();
    
    // For now, just clear tokens locally
    // This will be handled in the slice
    return;
  } catch (error: any) {
    return rejectWithValue(error.message || 'Logout failed');
  }
});
