{"version": "0.2.0", "configurations": [{"type": "node", "request": "launch", "name": "Run metro-symbolicate", "runtimeExecutable": "metro-symbolicate", "cwd": "${workspaceFolder}", "args": []}, {"type": "node", "request": "launch", "name": "Run metro", "runtimeExecutable": "metro", "cwd": "${workspaceFolder}", "args": []}, {"name": "Run iOS - Experimental", "request": "launch", "type": "reactnative", "cwd": "${workspaceFolder}", "enableDebug": false, "platform": "ios"}, {"name": "Debug Android - Experimental", "request": "launch", "type": "reactnative", "cwd": "${workspaceFolder}", "platform": "android"}, {"name": "Debug Android", "type": "reactnative", "request": "launch", "platform": "android", "cwd": "${workspaceFolder}"}, {"name": "Debug iOS", "type": "reactnative", "request": "launch", "platform": "ios", "cwd": "${workspaceFolder}"}]}