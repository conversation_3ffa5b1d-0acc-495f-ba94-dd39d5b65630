/**
 * Network Manager for handling API requests
 */

import axios, {
  AxiosInstance,
  InternalAxiosRequestConfig,
  AxiosResponse,
  AxiosError,
} from 'axios';
import {Alert} from 'react-native';
import NetworkConfig from './NetworkConfig';
import {
  HttpMethod,
  NetworkResponse,
  NetworkError,
  RequestConfig,
  AuthTokens,
  RefreshTokenResponse,
  MultipartFormData,
  FileUploadConfig,
} from './types';
import AsyncStorage from '@react-native-async-storage/async-storage';
class NetworkManager {
  private static instance: NetworkManager;
  private axiosInstance: AxiosInstance;
  private accessToken: string | null = null;
  private refreshToken: string | null = null;
  private isRefreshing = false;
  private failedQueue: Array<{
    resolve: (token: string) => void;
    reject: (error: any) => void;
  }> = [];

  private constructor() {
    this.axiosInstance = axios.create({
      baseURL: NetworkConfig.baseURL,
      timeout: NetworkConfig.timeout,
      headers: NetworkConfig.headers,
    });

    this.setupInterceptors();
    // Temporarily disabled until AsyncStorage is properly linked
    // this.loadTokensFromStorage();
  }

  /**
   * Load tokens from AsyncStorage on initialization
   * Currently disabled due to AsyncStorage linking issue
   */
  async loadTokensFromStorage(): Promise<void> {
    try {
      // Temporarily disabled - will be re-enabled after rebuild
      console.log('🔑 Token loading from storage temporarily disabled');
      return;

      const [accessToken, refreshToken] = await Promise.all([
        AsyncStorage.getItem('accessToken'),
        AsyncStorage.getItem('refreshToken'),
      ]);

      if (accessToken && refreshToken) {
        this.accessToken = accessToken;
        this.refreshToken = refreshToken;
        console.log('🔑 Tokens loaded from storage');
      }
    } catch (error) {
      console.error('❌ Error loading tokens from storage:', error);
    }
  }

  public static getInstance(): NetworkManager {
    if (!NetworkManager.instance) {
      NetworkManager.instance = new NetworkManager();
    }
    return NetworkManager.instance;
  }

  /**
   * Set authentication tokens and save to AsyncStorage
   * Currently using memory-only storage due to AsyncStorage linking issue
   */
  public async setAuthTokens(tokens: AuthTokens): Promise<void> {
    try {
      await Promise.all([
        AsyncStorage.setItem('accessToken', tokens.accessToken),
        AsyncStorage.setItem('refreshToken', tokens.refreshToken),
      ]);

      // Set in memory
      this.accessToken = tokens.accessToken;
      this.refreshToken = tokens.refreshToken;

      console.log('✅ Tokens saved to memory (storage temporarily disabled)');
    } catch (error) {
      console.error('❌ Error storing tokens:', error);
      // Still set in memory even if storage fails
      this.accessToken = tokens.accessToken;
      this.refreshToken = tokens.refreshToken;
    }
  }

  /**
   * Clear authentication tokens from memory and AsyncStorage
   * Currently using memory-only storage due to AsyncStorage linking issue
   */
  public async clearAuthTokens(): Promise<void> {
    try {
      // Temporarily disabled AsyncStorage - using memory only
      await Promise.all([
        AsyncStorage.removeItem('accessToken'),
        AsyncStorage.removeItem('refreshToken'),
      ]);

      // Clear from memory
      this.accessToken = null;
      this.refreshToken = null;

      console.log(
        '✅ Tokens cleared from memory (storage temporarily disabled)',
      );
    } catch (error) {
      console.error('❌ Error clearing tokens from storage:', error);
      // Still clear from memory even if storage fails
      this.accessToken = null;
      this.refreshToken = null;
    }
  }

  /**
   * Get current access token
   */
  public getAccessToken(): string | null {
    return this.accessToken;
  }

  /**
   * Get current refresh token
   */
  public getRefreshToken(): string | null {
    return this.refreshToken;
  }

  /**
   * Check if user is authenticated
   */
  public isAuthenticated(): boolean {
    return this.accessToken !== null && this.accessToken !== '';
  }

  /**
   * GET request
   */
  public async get<T>(
    url: string,
    config?: RequestConfig,
  ): Promise<NetworkResponse<T>> {
    return this.request<T>({
      method: HttpMethod.GET,
      url,
      config,
    });
  }

  /**
   * POST request
   */
  public async post<T>(
    url: string,
    data?: any,
    config?: RequestConfig,
  ): Promise<NetworkResponse<T>> {
    return this.request<T>({
      method: HttpMethod.POST,
      url,
      data,
      config,
    });
  }

  /**
   * PUT request
   */
  public async put<T>(
    url: string,
    data?: any,
    config?: RequestConfig,
  ): Promise<NetworkResponse<T>> {
    return this.request<T>({
      method: HttpMethod.PUT,
      url,
      data,
      config,
    });
  }

  /**
   * DELETE request
   */
  public async delete<T>(
    url: string,
    config?: RequestConfig,
  ): Promise<NetworkResponse<T>> {
    return this.request<T>({
      method: HttpMethod.DELETE,
      url,
      config,
    });
  }

  /**
   * PATCH request
   */
  public async patch<T>(
    url: string,
    data?: any,
    config?: RequestConfig,
  ): Promise<NetworkResponse<T>> {
    return this.request<T>({
      method: HttpMethod.PATCH,
      url,
      data,
      config,
    });
  }

  /**
   * Upload file(s) with multipart/form-data
   * @param url API endpoint
   * @param formData Array of form data items (files and text fields)
   * @param config Optional configuration including upload progress callback
   */
  public async uploadFiles<T>(
    url: string,
    formData: MultipartFormData[],
    config?: FileUploadConfig,
  ): Promise<NetworkResponse<T>> {
    // Create FormData object
    const multipartFormData = new FormData();

    // Add all form fields and files to FormData
    formData.forEach(item => {
      if (typeof item.value === 'string') {
        // Add text field
        multipartFormData.append(item.name, item.value);
      } else {
        // Add file
        const file = item.value;
        multipartFormData.append(item.name, {
          uri: file.uri,
          name: file.name,
          type: file.type,
        } as any);
      }
    });

    // Create request config with proper headers for multipart/form-data
    const uploadConfig: RequestConfig = {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      ...config,
    };

    // Make the request
    return this.request<T>({
      method: HttpMethod.POST,
      url,
      data: multipartFormData,
      config: uploadConfig,
    });
  }

  /**
   * Upload a single file with optional additional form fields
   * @param url API endpoint
   * @param fileField Name of the file field in the form
   * @param fileUri Local URI of the file to upload
   * @param fileName Name of the file (with extension)
   * @param fileType MIME type of the file
   * @param additionalFields Optional additional form fields
   * @param config Optional configuration including upload progress callback
   */
  public async uploadFile<T>(
    url: string,
    fileField: string,
    fileUri: string,
    fileName: string,
    fileType: string,
    additionalFields?: Record<string, string>,
    config?: FileUploadConfig,
  ): Promise<NetworkResponse<T>> {
    // Create form data array
    const formData: MultipartFormData[] = [
      {
        name: fileField,
        value: {
          uri: fileUri,
          name: fileName,
          type: fileType,
        },
      },
    ];

    // Add additional fields if provided
    if (additionalFields) {
      Object.entries(additionalFields).forEach(([key, value]) => {
        formData.push({
          name: key,
          value: value,
        });
      });
    }

    // Use the uploadFiles method
    return this.uploadFiles<T>(url, formData, config);
  }

  /**
   * Upload an image with optional compression and additional form fields
   * @param url API endpoint
   * @param imageField Name of the image field in the form
   * @param imageUri Local URI of the image to upload
   * @param imageName Name of the image file (with extension)
   * @param additionalFields Optional additional form fields
   * @param config Optional configuration including upload progress callback
   */
  public async uploadImage<T>(
    url: string,
    imageField: string,
    imageUri: string,
    imageName: string,
    additionalFields?: Record<string, string>,
    config?: FileUploadConfig,
  ): Promise<NetworkResponse<T>> {
    // Determine image MIME type based on file extension
    const extension = imageName.split('.').pop()?.toLowerCase() || '';
    let mimeType = 'image/jpeg'; // Default to JPEG

    if (extension === 'png') {
      mimeType = 'image/png';
    } else if (extension === 'gif') {
      mimeType = 'image/gif';
    } else if (extension === 'webp') {
      mimeType = 'image/webp';
    } else if (extension === 'heic' || extension === 'heif') {
      mimeType = 'image/heic';
    }

    // Use the uploadFile method
    return this.uploadFile<T>(
      url,
      imageField,
      imageUri,
      imageName,
      mimeType,
      additionalFields,
      config,
    );
  }

  /**
   * Generic request method
   */
  private async request<T>({
    method,
    url,
    data,
    config,
  }: {
    method: HttpMethod;
    url: string;
    data?: any;
    config?: RequestConfig;
  }): Promise<NetworkResponse<T>> {
    try {
      const response = await this.axiosInstance.request<T>({
        method,
        url,
        data,
        ...config,
      });

      return {
        data: response.data,
        status: response.status,
        headers: response.headers as Record<string, string>,
      };
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Handle network errors
   */
  private handleError(error: any): NetworkError {
    if (axios.isAxiosError(error)) {
      const axiosError = error as AxiosError;
      return {
        message: axiosError.message,
        status: axiosError.response?.status,
        data: axiosError.response?.data,
      };
    }
    return {
      message: error.message || 'Unknown error occurred',
    };
  }

  /**
   * Setup request and response interceptors
   */
  private setupInterceptors(): void {
    // Request interceptor
    this.axiosInstance.interceptors.request.use(
      async (config: InternalAxiosRequestConfig) => {
        // Ensure headers exist
        config.headers = config.headers || {};
        const token = await AsyncStorage.getItem('accessToken');
        if (token) {
          this.accessToken = token;
        }

        if (this.accessToken) {
          config.headers.Authorization = `Bearer ${this.accessToken}`;
        }
        return config;
      },
      error => Promise.reject(error),
    );

    // Response interceptor
    this.axiosInstance.interceptors.response.use(
      (response: AxiosResponse) => response,
      async (error: AxiosError) => {
        const originalRequest = error.config as InternalAxiosRequestConfig & {
          _retry?: boolean;
        };

        // Handle 401 Unauthorized error (token expired)
        if (
          error.response?.status === 401 &&
          !originalRequest._retry &&
          this.refreshToken
        ) {
          if (this.isRefreshing) {
            // Wait for the token refresh
            return new Promise<string>((resolve, reject) => {
              this.failedQueue.push({resolve, reject});
            })
              .then(token => {
                // Ensure headers exist
                originalRequest.headers = originalRequest.headers || {};
                originalRequest.headers.Authorization = `Bearer ${token}`;
                return this.axiosInstance(originalRequest);
              })
              .catch(err => Promise.reject(err));
          }

          originalRequest._retry = true;
          this.isRefreshing = true;

          try {
            const response = await axios.post<RefreshTokenResponse>(
              `${NetworkConfig.baseURL}/auth/refresh-token`,
              {refreshToken: this.refreshToken},
              {headers: {'Content-Type': 'application/json'}},
            );

            const {accessToken, refreshToken} = response.data;
            await this.setAuthTokens({accessToken, refreshToken});

            // Process the queue
            this.processQueue(null, accessToken);
            this.isRefreshing = false;

            // Retry the original request
            // Ensure headers exist
            originalRequest.headers = originalRequest.headers || {};
            originalRequest.headers.Authorization = `Bearer ${accessToken}`;
            return this.axiosInstance(originalRequest);
          } catch (refreshError) {
            this.processQueue(refreshError, null);
            this.isRefreshing = false;
            await this.clearAuthTokens();
            Alert.alert('Session expired', 'Please login again.');
            return Promise.reject(refreshError);
          }
        }

        // Handle network errors
        if (!error.response) {
          Alert.alert(
            'Network Error',
            'Please check your internet connection.',
          );
        } else if (error.response.status >= 500) {
          Alert.alert('Server Error', 'Please try again later.');
        }

        return Promise.reject(error);
      },
    );
  }

  /**
   * Process the queue of failed requests
   */
  private processQueue(error: any, token: string | null): void {
    this.failedQueue.forEach(promise => {
      if (error) {
        promise.reject(error);
      } else if (token) {
        promise.resolve(token);
      }
    });
    this.failedQueue = [];
  }
}

export default NetworkManager.getInstance();
