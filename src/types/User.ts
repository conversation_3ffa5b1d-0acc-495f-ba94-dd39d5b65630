/**
 * User related models
 */

export interface LoginRequest {
  email: string;
  password: string;
  deviceType: string;
  loginType: string;
  deviceToken: string;
  rememberMe?: boolean;
}

export interface RegisterRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phoneNumber?: string;
}

export interface VerifyResetPasswordRequest {
  email: string;
  resetPasswordCode: string;
}

export interface ResetPasswordRequest {
  resetPasswordCode: string;
  password: string;
  id: string;
}

export interface UserProfile {
  avatar: string;
  id: number;
  fullName: string;
  email: string;
  birthday: string;
  countryCode: string;
  mobile: string;
  mobileVerified: string;
  badge: number;
  notification: string;
  status: string;
  accessToken: string;
  cartItemAvailable: string;
  barName: string;
  loginType: string;
}
