/**
 * API Services for different domains
 */

import NetworkManager from '../network/NetworkManager';
import APIEndPoints from './APIEndPoints';
import {AuthTokens} from '../network/types';
import {
  LoginRequest,
  RegisterRequest,
  ResetPasswordRequest,
  UserProfile,
  VerifyResetPasswordRequest,
  BarListRequest,
} from '../../types/User';
import {BarDetails} from '../../types/BarDetails';
import {ApiResponse} from '../models/Response';

/**
 * Authentication Service
 */
export class AuthService {
  /**
   * Login user
   */
  static async login(params: LoginRequest): Promise<ApiResponse<UserProfile>> {
    const response = await NetworkManager.post<ApiResponse<UserProfile>>(
      APIEndPoints.AUTH.LOGIN,
      params,
    );
    console.log('🚀 ~ AuthService ~ login ~ response:', response);

    if (response.data.success && response.data.data) {
      const userData = response.data.data as UserProfile;

      // Store tokens if login successful
      if (userData.accessToken) {
        NetworkManager.setAuthTokens({
          accessToken: userData.accessToken,
          refreshToken: userData.accessToken,
        });
      }
    }

    return response.data;
  }

  /**
   * Register user
   */
  static async register(
    params: RegisterRequest,
  ): Promise<ApiResponse<AuthTokens>> {
    const response = await NetworkManager.post<ApiResponse<AuthTokens>>(
      APIEndPoints.AUTH.REGISTER,
      params,
    );

    // Store tokens if registration successful
    if (response.data.success && response.data.data) {
      NetworkManager.setAuthTokens(response.data.data);
    }

    return response.data;
  }

  /**
   * Request password reset
   */
  static async forgotPassword(email: string): Promise<ApiResponse<null>> {
    const response = await NetworkManager.post<ApiResponse<null>>(
      APIEndPoints.AUTH.FORGOT_PASSWORD,
      {email},
    );

    return response.data;
  }
  /**
   * Verify reset password code
   */

  static async verifyResetPassword(
    params: VerifyResetPasswordRequest,
  ): Promise<ApiResponse<AuthTokens>> {
    const response = await NetworkManager.post<ApiResponse<AuthTokens>>(
      APIEndPoints.AUTH.VERIFY_RESET_PASSWORD,
      params,
    );

    return response.data;
  }

  /**
   * Reset password with token
   */
  static async resetPassword(
    params: ResetPasswordRequest,
  ): Promise<ApiResponse<AuthTokens>> {
    const response = await NetworkManager.post<ApiResponse<AuthTokens>>(
      APIEndPoints.AUTH.RESET_PASSWORD,
      params,
    );

    return response.data;
  }
}

/**
 * User Service
 */
export class UserService {
  /**
   * Get user profile
   */
  static async getProfile(): Promise<ApiResponse<UserProfile>> {
    const response = await NetworkManager.get<ApiResponse<UserProfile>>(
      APIEndPoints.USER.PROFILE,
    );

    return response.data;
  }

  /**
   * Update user profile
   */
  static async updateProfile(
    profile: Partial<UserProfile>,
  ): Promise<ApiResponse<UserProfile>> {
    const response = await NetworkManager.put<ApiResponse<UserProfile>>(
      APIEndPoints.USER.UPDATE_PROFILE,
      profile,
    );

    return response.data;
  }
}

/**
 * Venue Service
 */
export class VenueService {
  /**
   * Get venues list
   */

  static async getBarList(
    params: BarListRequest,
  ): Promise<ApiResponse<BarDetails>> {
    const response = await NetworkManager.post<ApiResponse<BarDetails>>(
      APIEndPoints.VENUE.BAR_LIST,
      params,
    );
    console.log('🚀 ~ Barlist ~ response:', response);

    if (response.data.success && response.data.data) {
      const userData = response.data.data as BarDetails;
    }

    return response.data;
  }

  // /**
  //  * Get venue details
  //  */
  // static async getVenueDetails(id: string): Promise<ApiResponse<any>> {
  //   const response = await NetworkManager.get<ApiResponse<any>>(
  //     APIEndPoints.VENUE.VENUE_DETAILS(id),
  //   );

  //   return response.data;
  // }
}
