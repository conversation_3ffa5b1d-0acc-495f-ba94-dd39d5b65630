/**
 * Authentication utility functions
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import {NetworkManager} from '../services/network';

/**
 * Get access token from multiple sources
 * Priority: AsyncStorage > NetworkManager > null
 */
export const getAccessToken = async (): Promise<string | null> => {
  try {
    // Try AsyncStorage first
    const accessToken = await AsyncStorage.getItem('accessToken');
    if (accessToken && accessToken.trim() !== '') {
      console.log('✅ Access token found in AsyncStorage');
      return accessToken;
    }
  } catch (error) {
    console.log('⚠️ AsyncStorage not available, trying NetworkManager...');
  }

  // Fallback to NetworkManager (memory-only)
  try {
    const networkToken = NetworkManager.getAccessToken();
    if (networkToken && networkToken.trim() !== '') {
      console.log('✅ Access token found in NetworkManager');
      return networkToken;
    }
  } catch (error) {
    console.log('⚠️ NetworkManager not available');
  }

  console.log('❌ No access token found');
  return null;
};

/**
 * Check if user is authenticated
 */
export const isAuthenticated = async (): Promise<boolean> => {
  const token = await getAccessToken();
  return token !== null;
};

/**
 * Save access token to AsyncStorage
 */
export const saveAccessToken = async (token: string): Promise<void> => {
  try {
    await AsyncStorage.setItem('accessToken', token);
    console.log('✅ Access token saved to AsyncStorage');
  } catch (error) {
    console.error('❌ Error saving access token:', error);
    throw error;
  }
};

/**
 * Remove access token from AsyncStorage
 */
export const removeAccessToken = async (): Promise<void> => {
  try {
    await AsyncStorage.removeItem('accessToken');
    console.log('✅ Access token removed from AsyncStorage');
  } catch (error) {
    console.error('❌ Error removing access token:', error);
    throw error;
  }
};

/**
 * Get refresh token from AsyncStorage
 */
export const getRefreshToken = async (): Promise<string | null> => {
  try {
    const refreshToken = await AsyncStorage.getItem('refreshToken');
    if (refreshToken && refreshToken.trim() !== '') {
      console.log('✅ Refresh token found in AsyncStorage');
      return refreshToken;
    }
  } catch (error) {
    console.log('⚠️ AsyncStorage not available for refresh token');
  }

  // Fallback to NetworkManager
  try {
    const networkToken = NetworkManager.getRefreshToken();
    if (networkToken && networkToken.trim() !== '') {
      console.log('✅ Refresh token found in NetworkManager');
      return networkToken;
    }
  } catch (error) {
    console.log('⚠️ NetworkManager not available for refresh token');
  }

  console.log('❌ No refresh token found');
  return null;
};

/**
 * Initialize authentication on app startup
 * This loads tokens from storage and sets them in NetworkManager
 */
export const initializeAuth = async (): Promise<boolean> => {
  try {
    const [accessToken, refreshToken] = await Promise.all([
      getAccessToken(),
      getRefreshToken(),
    ]);

    if (accessToken) {
      // Set tokens in NetworkManager for API calls
      await NetworkManager.setAuthTokens({
        accessToken,
        refreshToken: refreshToken || accessToken, // Use access token as fallback
      });
      
      console.log('✅ Authentication initialized successfully');
      return true;
    }

    console.log('❌ No tokens found during initialization');
    return false;
  } catch (error) {
    console.error('❌ Error initializing authentication:', error);
    return false;
  }
};

/**
 * Clear all authentication data
 */
export const clearAuth = async (): Promise<void> => {
  try {
    await Promise.all([
      removeAccessToken(),
      AsyncStorage.removeItem('refreshToken'),
      NetworkManager.clearAuthTokens(),
    ]);
    console.log('✅ All authentication data cleared');
  } catch (error) {
    console.error('❌ Error clearing authentication data:', error);
    throw error;
  }
};
