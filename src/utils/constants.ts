import ForgotPasswordScreen from '../screens/ForgotPasswordScreen';
import ResetPasswordScreen from '../screens/ResetPasswordScreen';

const AlertMessages = {
  error: {
    emailRequired: 'Email is required.',
    passwordRequired: 'Password is required.',
    invalidEmail: 'Please enter a valid email address.',
    shortPassword: 'Password must be at least 6 characters.',
    invalidOTP: 'Please enter a valid OTP.',
    sortOTP: 'OTP must be 6 digits.',
  },
  success: {
    loginSuccess: 'Login successful!',
  },
};

const AlertTitles = {
  error: 'Error',
  success: 'Success',
};

const Placeholders = {
  email: 'Enter email',
  password: 'Password',
  opt: 'Enter OTP',
};

const StaticContents = {
  loginScreen: {
    loginButton: 'Login',
    loginTitle: 'Welcome back!',
    loginSubtitle: 'All your favourite venues in one place.',
    noAccount: 'Don`t have an account?',
  },
  ForgotPasswordScreen: {
    forgotPassTitle: 'Reset Your Password',
    forgotPassSubtitle:
      'Forgot your password? Please enter your email to receive a verification code to reset your password.',
  },
  OTPVerificarionScreen: {
    otpTitle: 'Enter Your Verification Code',
    otpSubtitle:
      'Please enter the code sent to {email}. Did not receive an email? Please check your junk folder, or click here to resend.',
  },
  ResetPasswordScreen: {
    resetPassTitle: 'Create Your New Password Your Password',
    resetPassSubtitle:
      'Please create a new password to access your account. For security purposes, please ensure your password is more than 6 digits.se enter your new password.',
  },
};

const Icons = {
  eye: 'eye',
  eye_off: 'eye-off',
};

const AssetImages = {
  ic_back_icon: require('../assets/images/ic_back_icon.png'),
  ic_header_logo: require('../assets/images/ic_header_logo.png'),
};

const ButtonTitle = {
  forgotPassword: 'Forgot Password?',
  rememberMe: 'Remember Me',
  login: 'Login',
  signup: 'Sign Up',
  continue: 'Continue',
  verify: 'Verify',
  saveNewpass: 'Save new password',
};

export {
  AlertMessages,
  AlertTitles,
  Placeholders,
  StaticContents,
  Icons,
  ButtonTitle,
  AssetImages,
};
