import React, {use} from 'react';
import {View, Text} from 'react-native';
import styles from '../styles/commonStyles';
import HeaderView from '../components/HeaderView/index';
import {RootStackParamList} from '../navigation/AppNavigator';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AssetImages} from '../utils/constants';
import {TopSafeAreaView} from '../components/CustomSafeAreaView/index';
import {useAppSelector} from '../redux/store/hooks';
type Props = NativeStackScreenProps<RootStackParamList, 'Home'>;

const HomeScreen: React.FC<Props> = ({navigation}) => {
  const user = useAppSelector(state => state.user);
  console.log('🚀 ~ user:', user);

  return (
    <>
      <TopSafeAreaView />
      <HeaderView
        leftIcon={AssetImages.ic_back_icon}
        onPress={() => navigation.goBack()}
      />
      <View style={styles.container}>
        <Text>{`Hello ${user.fullName}!\nWelcome to the Home Screen!`}</Text>
      </View>
    </>
  );
};

export default HomeScreen;
