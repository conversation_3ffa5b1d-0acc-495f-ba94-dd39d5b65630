import React, {useEffect} from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import AsyncStorage from '@react-native-async-storage/async-storage';
import LoginScreen from '../screens/LoginScreen';
import HomeScreen from '../screens/HomeScreen';
import ForgotPasswordScreen from '../screens/ForgotPasswordScreen';
import OtpVerificationScreen from '../screens/OtpVerificationScreen';
import ResetPasswordScreen from '../screens/ResetPasswordScreen';
import {useAppSelector, useAppDispatch} from '../redux/store/hooks';
import {loginUser} from '../redux/store/slices/userSlice';

export type RootStackParamList = {
  Login: undefined;
  Home: undefined;
  ForgotPassword: undefined;
  OtpVerification: {email: string};
  ResetPassword: {resetPasswordCode: number; id: string};
};

const Stack = createNativeStackNavigator<RootStackParamList>();

const AppNavigator = () => {
  const user = useAppSelector(state => state.user);
  const dispatch = useAppDispatch();

  // Load token from AsyncStorage on app startup
  useEffect(() => {
    const loadStoredToken = async () => {
      try {
        const storedToken = await AsyncStorage.getItem('accessToken');
        if (storedToken && storedToken.trim() !== '') {
          // Set token in Redux store
          dispatch(
            loginUser({
              ...user,
              accessToken: storedToken,
            }),
          );
          console.log('✅ Token loaded from storage and set in Redux');
        }
      } catch (error) {
        console.log('⚠️ Could not load token from storage:', error);
      }
    };

    // Only load if Redux doesn't already have a token
    if (!user.accessToken || user.accessToken.trim() === '') {
      loadStoredToken();
    }
  }, [dispatch, user]);

  // Simple check: if user has accessToken in Redux, go to Home, otherwise Login
  const isAuthenticated = user.accessToken && user.accessToken.trim() !== '';

  return (
    <Stack.Navigator
      initialRouteName={isAuthenticated ? 'Home' : 'Login'}
      screenOptions={{headerShown: false}}>
      <Stack.Screen name="Login" component={LoginScreen} />
      <Stack.Screen name="Home" component={HomeScreen} />
      <Stack.Screen name="ForgotPassword" component={ForgotPasswordScreen} />
      <Stack.Screen name="OtpVerification" component={OtpVerificationScreen} />
      <Stack.Screen name="ResetPassword" component={ResetPasswordScreen} />
    </Stack.Navigator>
  );
};
export default AppNavigator;
