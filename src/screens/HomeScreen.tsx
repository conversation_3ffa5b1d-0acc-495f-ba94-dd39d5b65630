import React, {useEffect, useState} from 'react';
import {View, Text, ActivityIndicator, Alert} from 'react-native';
import HeaderView from '../components/HeaderView/index';
import {RootStackParamList} from '../navigation/AppNavigator';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {TopSafeAreaView} from '../components/CustomSafeAreaView/index';
import {useAppDispatch, useAppSelector} from '../redux/store/hooks';
import {FlatList} from 'react-native-gesture-handler';
import {VenueService} from '../services/api';
import {BarDetails} from '../types/BarDetails';
import {BarListRequest} from '../types/User';

type Props = NativeStackScreenProps<RootStackParamList, 'Home'>;

const HomeScreen: React.FC<Props> = ({navigation}) => {
  const dispatch = useAppDispatch();

  // State for managing bar list data and loading
  const [barList, setBarList] = useState<BarDetails>();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const barlistWSCall = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const requestParams: BarListRequest = {
        latitude: '22.9857',
        longitude: '72.6432',
        search: '',
        showAll: 1,
        page: 1,
        serviceType: 'BOTH',
        distanceKm: 50,
      };

      const response = await VenueService.getBarList(requestParams);
      console.log('🚀 ~ barlistWSCall ~ response:', response);

      if (response.success && response.data) {
        setBarList(response.data);
        // console.log(
        //   '✅ Bar list loaded successfully:',
        //   response.data.length,
        //   'bars',
        // );
      } else {
        const errorMessage = response.message || 'Failed to load bar list';
        setError(errorMessage);
        Alert.alert('Error', errorMessage);
      }
    } catch (error: any) {
      console.error('❌ Error loading bar list:', error);
      const errorMessage = error.message || 'An unexpected error occurred';
      setError(errorMessage);
      Alert.alert('Error', errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Call barlistWSCall when the screen opens
  useEffect(() => {
    console.log('🏠 HomeScreen mounted - calling barlistWSCall');
    barlistWSCall();
  }, []); // Empty dependency array means this runs once when component mounts

  // Optional: Add a refresh function that can be called manually
  const refreshBarList = () => {
    console.log('🔄 Refreshing bar list');
    barlistWSCall();
  };

  const renderBarItem = ({item}: {item: BarDetails}) => (
    <View
      style={{padding: 15, borderBottomWidth: 1, borderBottomColor: '#eee'}}>
      <Text style={{fontSize: 16, fontWeight: 'bold'}}>{item.name}</Text>
      <Text style={{fontSize: 14, color: '#666'}}>{item.address}</Text>
      <Text style={{fontSize: 12, color: '#999'}}>
        {item.city}, {item.state}
      </Text>
    </View>
  );

  return (
    <>
      <TopSafeAreaView />
      <HeaderView />

      {/* <View style={{flex: 1, padding: 16}}>
        {isLoading && (
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'center',
              alignItems: 'center',
              padding: 20,
            }}>
            <ActivityIndicator size="large" color="#007AFF" />
            <Text style={{marginLeft: 10, fontSize: 16}}>Loading bars...</Text>
          </View>
        )}

        {error && !isLoading && (
          <View
            style={{
              padding: 20,
              backgroundColor: '#ffebee',
              borderRadius: 8,
              marginBottom: 16,
            }}>
            <Text style={{color: '#c62828', fontSize: 14}}>Error: {error}</Text>
            <Text
              style={{
                color: '#1976d2',
                fontSize: 14,
                marginTop: 8,
                textDecorationLine: 'underline',
              }}
              onPress={refreshBarList}>
              Tap to retry
            </Text>
          </View>
        )}

        {!isLoading && !error && barList.length === 0 && (
          <View style={{padding: 20, alignItems: 'center'}}>
            <Text style={{fontSize: 16, color: '#666'}}>No bars found</Text>
            <Text
              style={{
                color: '#1976d2',
                fontSize: 14,
                marginTop: 8,
                textDecorationLine: 'underline',
              }}
              onPress={refreshBarList}>
              Tap to refresh
            </Text>
          </View>
        )}

        {!isLoading && barList.length > 0 && (
          <View style={{flex: 1}}>
            <Text style={{fontSize: 18, fontWeight: 'bold', marginBottom: 16}}>
              Found {barList.length} bars nearby
            </Text>
            <FlatList
              data={barList}
              renderItem={renderBarItem}
              keyExtractor={item => item.id.toString()}
              showsVerticalScrollIndicator={false}
              refreshing={isLoading}
              onRefresh={refreshBarList}
            />
          </View>
        )}
      </View> */}
    </>
  );
};

export default HomeScreen;
